# Word文档组织工具

这个JavaScript脚本可以根据目录文档自动组织和重命名Word文档。

## 功能特点

1. **自动识别目录文件**: 查找文件名包含"目录"的Word文档
2. **智能匹配**: 使用相似度算法匹配文档名称与目录条目
3. **自动重命名**: 按目录顺序将文档重命名为 `1-原文件名.docx`, `2-原文件名.docx` 格式
4. **详细日志**: 显示匹配过程和相似度分数

## 安装依赖

```bash
npm install
```

## 使用方法

1. **修改文件夹路径**: 编辑 `organize-word-docs.js` 文件中的 `FOLDER_PATH` 变量，设置为你的Word文档所在文件夹的完整路径。

```javascript
const FOLDER_PATH = '/Users/<USER>/Downloads/高中数学/2026版 步步高 大一轮 数学 人教A版 提高版（教师用书）-【123份】';
```

2. **运行脚本**:

```bash
npm start
```

或者直接运行：

```bash
node organize-word-docs.js
```

## 工作原理

1. **扫描文件夹**: 找到所有 `.doc` 和 `.docx` 文件
2. **识别目录文件**: 查找文件名包含"目录"的文档
3. **解析目录**: 从目录文档中提取文本内容，识别目录条目
4. **相似度匹配**: 使用编辑距离算法计算每个文档名与目录条目的相似度
5. **排序重命名**: 按目录顺序重命名文档为 `序号-原文件名` 格式

## 相似度算法

脚本使用编辑距离（Levenshtein Distance）算法计算字符串相似度：
- 相似度范围：0-1（1表示完全匹配）
- 自动清理文本：移除特殊字符和空格
- 智能过滤：排除明显不是目录条目的行

## 测试

运行测试脚本验证相似度计算：

```bash
npm test
```

## 注意事项

1. **备份文件**: 运行脚本前请备份原始文件
2. **文件权限**: 确保对目标文件夹有读写权限
3. **文件格式**: 支持 `.doc` 和 `.docx` 格式
4. **目录文件**: 确保存在包含"目录"的Word文档

## 输出示例

```
开始处理文件夹: /path/to/documents
找到 25 个Word文档
找到目录文件: 目录.docx
从目录中提取到 24 个条目
需要排序的文档数量: 24

匹配结果:
序号 | 相似度 | 原文件名 | 匹配的目录条目
--------------------------------------------------------------------------------
  1 | 0.856 | 集合与常用逻辑用语.docx | 第一章 集合与常用逻辑用语
  2 | 0.792 | 函数概念与性质.docx | 第二章 函数概念与性质
  ...

开始重命名文件...
重命名成功: 集合与常用逻辑用语.docx -> 1-集合与常用逻辑用语.docx
重命名成功: 函数概念与性质.docx -> 2-函数概念与性质.docx
...

文档组织完成！
```

## 故障排除

- **找不到目录文件**: 确保存在文件名包含"目录"的Word文档
- **权限错误**: 检查文件夹读写权限
- **依赖错误**: 运行 `npm install` 安装必要依赖
