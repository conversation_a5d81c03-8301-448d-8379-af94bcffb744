const fs = require('fs');
const path = require('path');
const mammoth = require('mammoth');

// 配置
const FOLDER_PATH = '/Users/<USER>/Downloads/高中数学/2026版 步步高 大一轮 数学 人教A版 提高版（教师用书）-【123份】';

// 字符串相似度计算函数（使用编辑距离）
function calculateSimilarity(str1, str2) {
    const len1 = str1.length;
    const len2 = str2.length;
    
    // 创建二维数组
    const dp = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));
    
    // 初始化
    for (let i = 0; i <= len1; i++) dp[i][0] = i;
    for (let j = 0; j <= len2; j++) dp[0][j] = j;
    
    // 计算编辑距离
    for (let i = 1; i <= len1; i++) {
        for (let j = 1; j <= len2; j++) {
            if (str1[i-1] === str2[j-1]) {
                dp[i][j] = dp[i-1][j-1];
            } else {
                dp[i][j] = Math.min(
                    dp[i-1][j] + 1,     // 删除
                    dp[i][j-1] + 1,     // 插入
                    dp[i-1][j-1] + 1    // 替换
                );
            }
        }
    }
    
    // 转换为相似度（0-1之间）
    const maxLen = Math.max(len1, len2);
    return maxLen === 0 ? 1 : 1 - (dp[len1][len2] / maxLen);
}

// 清理文本，移除特殊字符和空格，以及章节标识
function cleanText(text) {
    return text
        .replace(/第[（(]?[一二三四五六七八九十\d]+[）)]?章/g, '') // 去掉"第X章"
        .replace(/第[（(]?[一二三四五六七八九十\d]+[）)]?节/g, '') // 去掉"第X节"
        .replace(/^[一二三四五六七八九十\d]+[、．.]/g, '') // 去掉开头序号
        .replace(/^\d+\.\d+/g, '') // 去掉"1.1"格式
        .replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '') // 去掉特殊字符
        .toLowerCase();
}

// 从Word文档中提取文本
async function extractTextFromWord(filePath) {
    try {
        const result = await mammoth.extractRawText({ path: filePath });
        return result.value;
    } catch (error) {
        console.error(`读取文件失败: ${filePath}`, error.message);
        return '';
    }
}

// 解析目录内容，提取条目列表
function parseTableOfContents(content) {
    const lines = content.split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0)
        .filter(line => {
            // 过滤掉明显不是目录条目的行
            const cleaned = cleanText(line);
            return cleaned.length > 2 &&
                   !line.includes('目录') &&
                   !line.includes('页码') &&
                   !line.includes('Page') &&
                   !/^\d+$/.test(cleaned); // 排除纯数字行
        })
        .map(line => {
            // 去掉章节标识，如"第一章"、"第（一）章"、"第1章"等
            return line
                .replace(/第[（(]?[一二三四五六七八九十\d]+[）)]?章\s*/g, '') // 去掉"第X章"格式
                .replace(/第[（(]?[一二三四五六七八九十\d]+[）)]?节\s*/g, '') // 去掉"第X节"格式
                .replace(/^[一二三四五六七八九十\d]+[、．.]\s*/g, '') // 去掉开头的序号
                .replace(/^\d+\.\d+\s*/g, '') // 去掉"1.1"这种格式
                .trim();
        })
        .filter(line => line.length > 0); // 过滤掉处理后为空的行

    console.log(`从目录中提取到 ${lines.length} 个条目`);
    console.log(lines);
    return lines;
}

// 主函数
async function organizeWordDocuments() {
    try {
        console.log(`开始处理文件夹: ${FOLDER_PATH}`);
        
        // 检查文件夹是否存在
        if (!fs.existsSync(FOLDER_PATH)) {
            throw new Error(`文件夹不存在: ${FOLDER_PATH}`);
        }
        
        // 读取文件夹中的所有文件
        const files = fs.readdirSync(FOLDER_PATH)
            .filter(file => file.toLowerCase().endsWith('.docx') || file.toLowerCase().endsWith('.doc'))
            .map(file => ({
                name: file,
                path: path.join(FOLDER_PATH, file)
            }));
        
        console.log(`找到 ${files.length} 个Word文档`);
        
        // 找到目录文件
        const tocFile = files.find(file => file.name.includes('目录'));
        if (!tocFile) {
            throw new Error('未找到包含"目录"的文件');
        }
        
        console.log(`找到目录文件: ${tocFile.name}`);
        
        // 提取目录内容
        const tocContent = await extractTextFromWord(tocFile.path);
        const tocEntries = parseTableOfContents(tocContent);
        console.log(tocEntries)
        
        if (tocEntries.length === 0) {
            throw new Error('目录文件中未找到有效条目');
        }
        
        // 获取除目录文件外的所有文档
        const documentFiles = files.filter(file => file !== tocFile);
        console.log(`需要排序的文档数量: ${documentFiles.length}`);
        
        // 为每个文档计算与目录条目的相似度
        const matchResults = [];
        
        for (const doc of documentFiles) {
            const docNameCleaned = cleanText(path.parse(doc.name).name);
            let bestMatch = null;
            let bestSimilarity = 0;
            let bestIndex = -1;
            
            for (let i = 0; i < tocEntries.length; i++) {
                const entryCleaned = cleanText(tocEntries[i]);
                const similarity = calculateSimilarity(docNameCleaned, entryCleaned);
                
                if (similarity > bestSimilarity) {
                    bestSimilarity = similarity;
                    bestMatch = tocEntries[i];
                    bestIndex = i;
                }
            }
            
            matchResults.push({
                file: doc,
                matchedEntry: bestMatch,
                similarity: bestSimilarity,
                order: bestIndex + 1
            });
        }
        
        // 按目录顺序排序
        matchResults.sort((a, b) => a.order - b.order);
        
        // 显示匹配结果
        console.log('\n匹配结果:');
        console.log('序号 | 相似度 | 原文件名 | 匹配的目录条目');
        console.log('-'.repeat(80));
        
        matchResults.forEach((result, index) => {
            console.log(`${String(index + 1).padStart(3)} | ${result.similarity.toFixed(3)} | ${result.file.name} | ${result.matchedEntry}`);
        });
        
        // 重命名文件
        console.log('\n开始重命名文件...');
        
        for (let i = 0; i < matchResults.length; i++) {
            const result = matchResults[i];
            const oldPath = result.file.path;
            const fileExt = path.extname(result.file.name);
            const baseName = path.parse(result.file.name).name;
            const newName = `${i + 1}-${baseName}${fileExt}`;
            const newPath = path.join(FOLDER_PATH, newName);
            
            try {
                // 检查新文件名是否已存在
                if (fs.existsSync(newPath) && newPath !== oldPath) {
                    console.log(`跳过重命名 (文件已存在): ${newName}`);
                    continue;
                }
                
                fs.renameSync(oldPath, newPath);
                console.log(`重命名成功: ${result.file.name} -> ${newName}`);
            } catch (error) {
                console.error(`重命名失败: ${result.file.name}`, error.message);
            }
        }
        
        console.log('\n文档组织完成！');
        
    } catch (error) {
        console.error('处理过程中出现错误:', error.message);
    }
}

// 运行脚本
if (require.main === module) {
    organizeWordDocuments();
}

module.exports = { organizeWordDocuments, calculateSimilarity };
