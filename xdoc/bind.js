const axios = require('axios');


const cookie = 'UBUS=-SLp1gRONZfONW0TsBQS9FsHUPB_QB_45tkHHZhsMP_jwmnypXyT11FDuO-0S5Hs'
const bind = async (taskId, bookId) => {
    await axios.post('http://xdoc.open.hexinedu.com/api/admin/task/bindBook', {
        bookId,
        taskId
    }, {
        headers: {
            cookie
        }
    })
}

const getTasks = async () => {
    const res = await axios.get('http://xdoc.open.hexinedu.com/api/admin/task/getListByAdmin?key=&page=1&pageSize=500&unpublished=false&notest=false&appKey=bf8ce6d1229bea9628b5b32b', {
        headers: {
            cookie
        }
    });
    const tasks = res.data.data.tasks;
    return tasks.filter(v => /^\d+-/.test(v.taskName));
}

const main = async () => {
    const tasks = await getTasks();
    console.log(tasks)
    // for (let i = 0; i < tasks.length; i++) {
    //     const task = tasks[i];
    //     await bind(task.id, '111111111111111111111111');
    //     console.log(`绑定任务成功: ${task.taskName}`);
    // }
}

main()