const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 读取 request_info.json 文件
const requestInfo = require('./request_info.json');

const sleep = (ms = 700) => new Promise(resolve => setTimeout(resolve, ms));

const createTask =  async (id, fileName) => {
    const task = { ...requestInfo };
    task.file.words.body[0].id = id;
    task.file.words.body[0].name = fileName;
    task.file.words.body[0].url = `https://hexin-worksheet.oss-cn-shanghai.aliyuncs.com/files/${id}.docx`;
    task.taskName = fileName;
    const res = await axios.post('http://xdoc.open.hexinedu.com/api/admin/taskV2/wordCreate', task, {
        headers: {
            cookie: 'UBUS=-SLp1gRONZfONW0TsBQS9FsHUPB_QB_45tkHHZhsMP_jwmnypXyT11FDuO-0S5Hs'
        }
    })
    console.log(`创建任务成功: ${fileName}`);
    await sleep();
    return res.data.data.taskId;
}

const main = async () => {
    const mappingJSON = JSON.parse(fs.readFileSync(path.join(__dirname, 'mapping.json'), 'utf8'));
    // 1-第一章　§1.1　集　合
    const mappings = mappingJSON.mappings.sort((a, b) => a.wordName.split('-')[0] - b.wordName.split('-')[0]);
    const res = [];
    for (let i = 0; i < mappings.length; i++) {
        const mapping = mappings[i];
        const taskId = await createTask(mapping.id, mapping.wordName);
        res.push(taskId);
    }
    fs.writeFileSync(path.join(__dirname, 'taskIds.json'), JSON.stringify(res));
}

main()