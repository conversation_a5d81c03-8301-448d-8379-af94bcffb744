const Oss = require('ali-oss');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs-extra');
const path = require('path');

// OSS 配置信息
const ossInfo = {
  region: 'oss-cn-shanghai',
  accessKeyId: 'G2kh0AfonFa8hNBe',
  accessKeySecret: 'co6wZce7knxINgRDMlwIALNwrOsD7T',
  bucket: 'hexin-worksheet',
};

const client = new Oss({
  region: ossInfo.region,
  accessKeyId: ossInfo.accessKeyId,
  accessKeySecret: ossInfo.accessKeySecret,
  bucket: ossInfo.bucket,
});

// 计算文件的 MD5 值
function calculateMD5(filePath) {
  return new Promise((resolve, reject) => {
    const hash = crypto.createHash('md5');
    const stream = fs.createReadStream(filePath);

    stream.on('data', (data) => {
      hash.update(data);
    });

    stream.on('end', () => {
      resolve(hash.digest('hex'));
    });

    stream.on('error', (err) => {
      reject(err);
    });
  });
}

// 使用 multipart 上传文件
async function uploadFileWithMultipart(filePath, objectKey) {
  try {
    console.log(`开始上传文件: ${filePath} -> ${objectKey}`);

    // 使用 multipart 上传
    const result = await client.multipartUpload(objectKey, filePath, {
      progress: (p, cpt) => {
        console.log(`上传进度: ${Math.round(p * 100)}%`);
      },
      partSize: 1024 * 1024, // 1MB per part
    });

    console.log(`文件上传成功: ${objectKey}`);
    return result;
  } catch (error) {
    console.error(`文件上传失败: ${error.message}`);
    throw error;
  }
}

// 处理单个 docx 文件
async function processDocxFile(filePath) {
  try {
    const fileName = path.basename(filePath, '.docx');
    const md5Hash = await calculateMD5(filePath);
    const uuid = uuidv4().replace(/-/g, ''); // 生成类似 'd002c34aa5a4b2df370f0784f743a71b' 的格式
    const objectKey = `files/${uuid}.docx`; // 上传到 files/ 目录下

    console.log(`处理文件: ${fileName}`);
    console.log(`MD5: ${md5Hash}`);
    console.log(`UUID: ${uuid}`);
    console.log(`OSS路径: ${objectKey}`);

    // 上传文件到 files/ 目录
    await uploadFileWithMultipart(filePath, objectKey);

    return {
      id: uuid,
      wordName: fileName,
      md5: md5Hash,
      originalPath: filePath,
      ossPath: objectKey
    };
  } catch (error) {
    console.error(`处理文件失败 ${filePath}: ${error.message}`);
    throw error;
  }
}

// 扫描目录中的所有 docx 文件
function findDocxFiles(directory) {
  const docxFiles = [];

  function scanDirectory(dir) {
    const items = fs.readdirSync(dir);

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        scanDirectory(fullPath);
      } else if (path.extname(item).toLowerCase() === '.docx') {
        docxFiles.push(fullPath);
      }
    }
  }

  scanDirectory(directory);
  return docxFiles;
}

// 保存 id-wordname 映射到文件
async function saveMapping(mappings, outputFile) {
  try {
    const mappingData = {
      timestamp: new Date().toISOString(),
      totalFiles: mappings.length,
      mappings: mappings
    };

    await fs.writeJson(outputFile, mappingData, { spaces: 2 });
    console.log(`映射文件已保存到: ${outputFile}`);

    // 同时保存一个简化版本，只包含 id 和 wordName
    const simpleMapping = {};
    mappings.forEach(item => {
      simpleMapping[item.id] = item.wordName;
    });

    const simpleMappingFile = outputFile.replace('.json', '_simple.json');
    await fs.writeJson(simpleMappingFile, simpleMapping, { spaces: 2 });
    console.log(`简化映射文件已保存到: ${simpleMappingFile}`);

  } catch (error) {
    console.error(`保存映射文件失败: ${error.message}`);
    throw error;
  }
}

// 主函数
async function main(inputDirectory, outputFile) {
  try {
    console.log('=== DOCX 文件上传工具 ===');
    console.log(`输入目录: ${inputDirectory}`);
    console.log(`输出文件: ${outputFile}`);

    // 检查输入目录是否存在
    if (!await fs.pathExists(inputDirectory)) {
      throw new Error(`输入目录不存在: ${inputDirectory}`);
    }

    // 查找所有 docx 文件
    console.log('\n正在扫描 docx 文件...');
    const docxFiles = findDocxFiles(inputDirectory);
    console.log(`找到 ${docxFiles.length} 个 docx 文件`);

    if (docxFiles.length === 0) {
      console.log('没有找到 docx 文件，程序结束');
      return;
    }

    // 处理每个文件
    const mappings = [];
    const keyValueStore = new Map(); // 用于存储 id 和 word 的键值对

    for (let i = 0; i < docxFiles.length; i++) {
      const filePath = docxFiles[i];
      console.log(`\n[${i + 1}/${docxFiles.length}] 处理文件: ${path.basename(filePath)}`);

      try {
        const result = await processDocxFile(filePath);
        mappings.push(result);

        // 存储到 key-value
        keyValueStore.set(result.id, result.wordName);

        console.log(`✓ 完成: ${result.wordName} -> ${result.id}`);
      } catch (error) {
        console.error(`✗ 失败: ${path.basename(filePath)} - ${error.message}`);
      }
    }

    // 保存映射文件
    console.log('\n正在保存映射文件...');
    await saveMapping(mappings, outputFile);

    // 显示统计信息
    console.log('\n=== 上传完成 ===');
    console.log(`成功上传: ${mappings.length} 个文件`);
    console.log(`失败: ${docxFiles.length - mappings.length} 个文件`);
    console.log(`Key-Value 存储条目: ${keyValueStore.size} 个`);

    // 显示 key-value 存储内容
    console.log('\nKey-Value 存储内容:');
    for (const [id, wordName] of keyValueStore) {
      console.log(`${id} -> ${wordName}`);
    }

  } catch (error) {
    console.error(`程序执行失败: ${error.message}`);
    process.exit(1);
  }
}

// 命令行参数处理
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args.length < 1) {
    console.log('使用方法:');
    console.log('  node index.js <输入目录> [输出文件]');
    console.log('');
    console.log('示例:');
    console.log('  node index.js ./docs');
    console.log('  node index.js ./docs ./output/mapping.json');
    process.exit(1);
  }

  const inputDirectory = args[0];
  const outputFile = args[1] || './id_word_mapping.json';

  main(inputDirectory, outputFile);
}

module.exports = {
  calculateMD5,
  uploadFileWithMultipart,
  processDocxFile,
  findDocxFiles,
  saveMapping,
  main
};